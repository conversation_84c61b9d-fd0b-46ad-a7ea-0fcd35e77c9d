package com.kingsoft.ai.hanhai.basematrix.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.lock.annotation.Lock4j;
import com.kingsoft.ai.hanhai.basematrix.common.core.constant.TenantConstants;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.R;
import com.kingsoft.ai.hanhai.basematrix.common.core.validate.AddGroup;
import com.kingsoft.ai.hanhai.basematrix.common.core.validate.EditGroup;
import com.kingsoft.ai.hanhai.basematrix.common.encrypt.annotation.ApiEncrypt;
import com.kingsoft.ai.hanhai.basematrix.common.excel.utils.ExcelUtil;
import com.kingsoft.ai.hanhai.basematrix.common.idempotent.annotation.RepeatSubmit;
import com.kingsoft.ai.hanhai.basematrix.common.json.utils.JsonUtils;
import com.kingsoft.ai.hanhai.basematrix.common.log.annotation.Log;
import com.kingsoft.ai.hanhai.basematrix.common.log.enums.BusinessType;
import com.kingsoft.ai.hanhai.basematrix.common.mybatis.core.page.PageQuery;
import com.kingsoft.ai.hanhai.basematrix.common.mybatis.core.page.TableDataInfo;
import com.kingsoft.ai.hanhai.basematrix.common.tenant.helper.TenantHelper;
import com.kingsoft.ai.hanhai.basematrix.common.web.core.BaseController;
import com.kingsoft.ai.hanhai.basematrix.system.config.UserChangeEventPublisher;
import com.kingsoft.ai.hanhai.basematrix.system.domain.bo.SysTenantBo;
import com.kingsoft.ai.hanhai.basematrix.system.domain.vo.SysTenantVo;
import com.kingsoft.ai.hanhai.basematrix.system.service.ISysTenantService;
import com.kingsoft.ai.hanhai.basematrix.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 租户管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/tenant")
@ConditionalOnProperty(value = "tenant.enable", havingValue = "true")
public class SysTenantController extends BaseController {

    private final ISysTenantService tenantService;
    private final ISysUserService userService;

    /**
     * 查询租户列表
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/list")
    public TableDataInfo<SysTenantVo> list(SysTenantBo bo, PageQuery pageQuery) {
        return tenantService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询租户详细列表
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/listDetails")
    public TableDataInfo<SysTenantVo> listDetails(SysTenantBo bo, PageQuery pageQuery) {
        return tenantService.queryPageListDetails(bo, pageQuery);
    }

    /**
     * 导出租户列表
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:export")
    @Log(title = "租户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysTenantBo bo, HttpServletResponse response) {
        List<SysTenantVo> list = tenantService.queryList(bo);
        ExcelUtil.exportExcel(list, "租户", SysTenantVo.class, response);
    }

    /**
     * 获取租户详细信息
     *
     * @param id 主键
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:query")
    @GetMapping("/{id}")
    public R<SysTenantVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(tenantService.queryById(id));
    }

    /**
     * 新增租户
     */
    @ApiEncrypt
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:add")
    @Log(title = "租户管理", businessType = BusinessType.INSERT)
    @Lock4j
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysTenantBo bo) {
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("新增租户'" + bo.getCompanyName() + "'失败，企业名称已存在");
        }
        bo.setTenantName(bo.getCompanyName());
        return toAjax(TenantHelper.ignore(() -> tenantService.insertByBo(bo)));
    }

    /**
     * 修改租户
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysTenantBo bo) {
        tenantService.checkTenantAllowed(bo.getTenantId());
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("修改租户'" + bo.getCompanyName() + "'失败，公司名称已存在");
        }
        bo.setTenantName(bo.getCompanyName());
        return toAjax(tenantService.updateByBo(bo));
    }

    /**
     * 状态修改
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysTenantBo bo) {
        tenantService.checkTenantAllowed(bo.getTenantId());
        int flag = tenantService.updateTenantStatus(bo);
        if (TenantConstants.TENANT_STATUS_DISABLE.equals(bo.getStatus())) {
            List<Long> ids = userService.selectUserIdsByTenantId(bo.getTenantId());
            //通知并下线用户
            if (!ids.isEmpty()) {
                for (Long id : ids) {
                    log.info("租户{}禁用，发送通知并下线用户ids {}", bo.getTenantId(), JsonUtils.toJsonString(ids));
                    UserChangeEventPublisher.publishTenantDisableEvent(ids);

                }
            } else {
                log.info("租户{}禁用，但没有用户下线", bo.getTenantId());
            }

        }
        return toAjax(flag);
    }

    /**
     * 删除租户
     *
     * @param ids 主键串
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:remove")
    @Log(title = "租户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        Boolean flag = tenantService.deleteWithValidByIds(Arrays.asList(ids), true);
        for (Long tenantId : ids) {
            List<Long> userIds = userService.selectUserIdsByTenantId(String.valueOf(tenantId));
            //通知并下线用户
            if (!userIds.isEmpty()){
                log.info("租户{}已删除，发送通知并下线用户ids {}", tenantId, JsonUtils.toJsonString(userIds));
                UserChangeEventPublisher.publishTenantDELETEEvent(userIds);
            }else{
                log.info("租户{}已删除，但没有用户下线", tenantId);
            }
        }
        return toAjax(flag);
    }

    /**
     * 动态切换租户
     *
     * @param tenantId 租户ID
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/dynamic/{tenantId}")
    public R<Void> dynamicTenant(@NotBlank(message = "租户ID不能为空") @PathVariable String tenantId) {
        TenantHelper.setDynamic(tenantId, true);
        return R.ok();
    }

    /**
     * 清除动态租户
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/dynamic/clear")
    public R<Void> dynamicClear() {
        TenantHelper.clearDynamic();
        return R.ok();
    }


    /**
     * 同步租户套餐
     *
     * @param tenantId  租户id
     * @param packageId 套餐id
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户管理", businessType = BusinessType.UPDATE)
    @GetMapping("/syncTenantPackage")
    public R<Void> syncTenantPackage(@NotBlank(message = "租户ID不能为空") String tenantId,
                                     @NotNull(message = "套餐ID不能为空") Long packageId) {
        return toAjax(TenantHelper.ignore(() -> tenantService.syncTenantPackage(tenantId, packageId)));
    }

    /**
     * 同步租户字典
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @Log(title = "租户管理", businessType = BusinessType.INSERT)
    @GetMapping("/syncTenantDict")
    public R<Void> syncTenantDict() {
        if (!TenantHelper.isEnable()) {
            return R.fail("当前未开启租户模式");
        }
        tenantService.syncTenantDict();
        return R.ok("同步租户字典成功");
    }

}
