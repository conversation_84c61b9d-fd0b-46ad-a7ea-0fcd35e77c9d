package com.kingsoft.ai.hanhai.basematrix.system.config;

import cn.hutool.core.collection.CollUtil;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.event.UserChangeEvent;
import com.kingsoft.ai.hanhai.basematrix.common.satoken.utils.LoginHelper;
import com.kingsoft.ai.hanhai.basematrix.common.tenant.helper.TenantHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;

/**
 * 用户变更事件发布工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class UserChangeEventPublisher {

    private static ApplicationEventPublisher eventPublisher;

    /**
     * 设置事件发布器
     */
    public static void setEventPublisher(ApplicationEventPublisher publisher) {
        eventPublisher = publisher;
    }

    /**
     * 发布租户禁用 事件
     */
    public static void publishTenantDisableEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.TENANT_DISABLE, userIds, "租户已被禁用");
    }

    /**
     * 发布租户删除事件
     */
    public static void publishTenantDELETEEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.TENANT_DELETE, userIds, "租户已被删除");
    }

    /**
     * 发布密码修改事件
     *
     * @param userId 用户ID
     */
    public static void publishPasswordChangeEvent(Long userId) {
        publishPasswordChangeEvent(List.of(userId));
    }

    /**
     * 发布密码修改事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishPasswordChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.PASSWORD_CHANGE, userIds, "用户密码已修改");
    }

    /**
     * 发布用户禁用事件
     *
     * @param userId 用户ID
     */
    public static void publishUserDisableEvent(Long userId) {
        publishUserDisableEvent(List.of(userId));
    }

    /**
     * 发布用户禁用事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishUserDisableEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.USER_DISABLE, userIds, "用户已被禁用");
    }

    /**
     * 发布用户删除事件
     *
     * @param userId 用户ID
     */
    public static void publishUserDeleteEvent(Long userId) {
        publishUserDeleteEvent(List.of(userId));
    }

    /**
     * 发布用户删除事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishUserDeleteEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.USER_DELETE, userIds, "用户已被删除");
    }

    /**
     * 发布用户修改事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishUserEditEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.USER_DELETE, userIds, "用户状态/角色/空间已被修改");
    }

    /**
     * 发布角色变更事件
     *
     * @param userId 用户ID
     */
    public static void publishRoleChangeEvent(Long userId) {
        publishRoleChangeEvent(List.of(userId));
    }

    /**
     * 发布角色变更事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishRoleChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.ROLE_CHANGE, userIds, "用户角色已变更");
    }

    /**
     * 发布角色状态变更事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishRoleStatusChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.ROLE_STATUS_CHANGE, userIds, "用户角色状态已变更");
    }
    /**
     * 发布角色删除事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishRoleDeleteEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.ROLE_DELETE, userIds, "用户角色已删除");
    }
    /**
     * 发布权限变更事件
     *
     * @param userId 用户ID
     */
    public static void publishPermissionChangeEvent(Long userId) {
        publishPermissionChangeEvent(List.of(userId));
    }

    /**
     * 发布权限变更事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishPermissionChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.PERMISSION_CHANGE, userIds, "用户权限已变更");
    }

    /**
     * 发布空间调整事件
     *
     * @param userId 用户ID
     */
    public static void publishSpaceChangeEvent(Long userId) {
        publishSpaceChangeEvent(List.of(userId));
    }

    /**
     * 发布空间调整事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishSpaceChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.SPACE_CHANGE, userIds, "用户空间权限已调整");
    }

    /**
     * 发布空间删除事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishSpaceDeleteEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.SPACE_DELETE, userIds, "空间已删除");
    }

    /**
     * 发布管理员重置密码事件
     *
     * @param userId 用户ID
     */
    public static void publishAdminResetPasswordEvent(Long userId) {
        publishAdminResetPasswordEvent(List.of(userId));
    }

    /**
     * 发布管理员重置密码事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishAdminResetPasswordEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.ADMIN_RESET_PASSWORD, userIds, "管理员已重置用户密码");
    }

    /**
     * 发布用户状态变更事件
     *
     * @param userId 用户ID
     */
    public static void publishUserStatusChangeEvent(Long userId) {
        publishUserStatusChangeEvent(List.of(userId));
    }

    /**
     * 发布用户状态变更事件
     *
     * @param userIds 用户ID列表
     */
    public static void publishUserStatusChangeEvent(List<Long> userIds) {
        publishEvent(UserChangeEvent.UserChangeType.USER_STATUS_CHANGE, userIds, "用户状态已变更");
    }

    /**
     * 发布用户变更事件
     *
     * @param changeType 变更类型
     * @param userIds 用户ID列表
     * @param reason 变更原因
     */
    public static void publishEvent(UserChangeEvent.UserChangeType changeType, List<Long> userIds, String reason) {
        if (eventPublisher == null) {
            log.warn("事件发布器未初始化，无法发布用户变更事件");
            return;
        }

        if (CollUtil.isEmpty(userIds)) {
            log.warn("用户ID列表为空，跳过事件发布");
            return;
        }

        try {
            UserChangeEvent event = new UserChangeEvent(changeType, userIds, reason);

            // 设置操作者信息
            try {
                event.setOperatorId(LoginHelper.getUserId());
                event.setOperatorName(LoginHelper.getUsername());
            } catch (Exception e) {
                log.debug("获取操作者信息失败: {}", e.getMessage());
            }

            // 设置租户信息
            try {
                event.setTenantId(TenantHelper.getTenantId());
            } catch (Exception e) {
                log.debug("获取租户信息失败: {}", e.getMessage());
            }

            eventPublisher.publishEvent(event);
            log.info("发布用户变更事件成功: 类型={}, 用户数量={}, 原因={}",
                changeType, userIds.size(), reason);

        } catch (Exception e) {
            log.error("发布用户变更事件失败", e);
        }
    }
}
