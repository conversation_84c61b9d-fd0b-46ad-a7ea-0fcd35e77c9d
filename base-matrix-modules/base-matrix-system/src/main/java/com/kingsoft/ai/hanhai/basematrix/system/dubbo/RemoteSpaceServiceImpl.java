package com.kingsoft.ai.hanhai.basematrix.system.dubbo;

import com.kingsoft.ai.hanhai.basematrix.common.core.utils.MapstructUtils;
import com.kingsoft.ai.hanhai.basematrix.common.tenant.helper.TenantHelper;
import com.kingsoft.ai.hanhai.basematrix.system.api.RemoteSpaceService;
import com.kingsoft.ai.hanhai.basematrix.system.api.domain.vo.RemoteSpaceVo;
import com.kingsoft.ai.hanhai.basematrix.system.domain.vo.SysSpaceVo;
import com.kingsoft.ai.hanhai.basematrix.system.mapper.SysSpaceMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
//@Mapping(path = "/system")
public class RemoteSpaceServiceImpl implements RemoteSpaceService {

    private final SysSpaceMapper spaceMapper;

    @Override
    //@Mapping(path = "/selectByTenantId")
    public List<RemoteSpaceVo> selectByTenantId(String tenantId) {
        return TenantHelper.dynamic(tenantId, () -> {
            List<SysSpaceVo> spaces = spaceMapper.selectVoList();
            return MapstructUtils.convert(spaces, RemoteSpaceVo.class);
        });
    }
}
