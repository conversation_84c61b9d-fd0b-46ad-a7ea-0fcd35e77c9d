package com.kingsoft.ai.hanhai.basematrix.system.dubbo;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kingsoft.ai.hanhai.basematrix.common.core.utils.MapstructUtils;
import com.kingsoft.ai.hanhai.basematrix.common.tenant.helper.TenantHelper;
import com.kingsoft.ai.hanhai.basematrix.system.api.RemoteSpaceService;
import com.kingsoft.ai.hanhai.basematrix.system.api.domain.vo.RemoteSpaceVo;
import com.kingsoft.ai.hanhai.basematrix.system.domain.SysUserSpace;
import com.kingsoft.ai.hanhai.basematrix.system.domain.vo.SysSpaceVo;
import com.kingsoft.ai.hanhai.basematrix.system.mapper.SysSpaceMapper;
import com.kingsoft.ai.hanhai.basematrix.system.mapper.SysUserSpaceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
//@Mapping(path = "/system")
public class RemoteSpaceServiceImpl implements RemoteSpaceService {

    private final SysSpaceMapper spaceMapper;
    private final SysUserSpaceMapper userSpaceMapper;

    @Override
    //@Mapping(path = "/selectByTenantId")
    public List<RemoteSpaceVo> selectByTenantId(String tenantId) {
        return TenantHelper.dynamic(tenantId, () -> {
            List<SysSpaceVo> spaces = spaceMapper.selectVoList();
            return MapstructUtils.convert(spaces, RemoteSpaceVo.class);
        });
    }

    @Override
    public RemoteSpaceVo getSpaceInfoById(Long spaceId) {
        SysSpaceVo sysSpaceVo = TenantHelper.ignore(() -> {
            return spaceMapper.selectSpaceById(spaceId);
        });
        log.info("获取space详情, spaceId: {}", spaceId);
        return MapstructUtils.convert(sysSpaceVo, RemoteSpaceVo.class);
    }

    @Override
    public Set<Long> selectSpaceIdsByUserId(Long userId) {
        List<SysUserSpace> sysUserSpaces = userSpaceMapper.selectList(new LambdaQueryWrapper<>(SysUserSpace.class)
            .eq(SysUserSpace::getUserId, userId));
        Set<Long> ownSpaceIds = new HashSet<>();
        for (SysUserSpace userSpace : sysUserSpaces) {
            Long spaceId = userSpace.getSpaceId();
            if (ObjectUtil.isNotEmpty(spaceId)) {
                ownSpaceIds.add(spaceId);
            }
        }
        return ownSpaceIds;
    }
}
