package com.kingsoft.ai.hanhai.basematrix.system.listener;

import com.kingsoft.ai.hanhai.basematrix.common.core.domain.event.UserChangeEvent;
import com.kingsoft.ai.hanhai.basematrix.system.config.UserOfflineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 用户变更事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserChangeEventListener {

    private final UserOfflineService userOfflineService;

    /**
     * 监听用户变更事件
     *
     * @param event 用户变更事件
     */
    //@Async 异步会导致sa-token上下文未初始化
    @EventListener
    public void handleUserChangeEvent(UserChangeEvent event) {
        log.info("接收到用户变更事件: {}", event);
        try {
            // 处理用户下线
            userOfflineService.handleUserChangeEvent(event);
        } catch (Exception e) {
            log.error("处理用户变更事件失败", e);
        }
    }
}
