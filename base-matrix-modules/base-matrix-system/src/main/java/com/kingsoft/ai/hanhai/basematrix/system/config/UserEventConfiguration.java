package com.kingsoft.ai.hanhai.basematrix.system.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 用户事件配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
@RequiredArgsConstructor
public class UserEventConfiguration {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 初始化事件发布器
     */
    @Bean
    public ApplicationRunner initEventPublisher() {
        return args -> {
            UserChangeEventPublisher.setEventPublisher(eventPublisher);
            log.info("用户变更事件发布器初始化完成");
        };
    }
}
