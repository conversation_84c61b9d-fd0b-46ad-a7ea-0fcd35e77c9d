package com.kingsoft.ai.hanhai.basematrix.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.kingsoft.ai.hanhai.basematrix.common.core.constant.TenantConstants;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.R;
import com.kingsoft.ai.hanhai.basematrix.common.core.exception.ServiceException;
import com.kingsoft.ai.hanhai.basematrix.common.core.validate.AddGroup;
import com.kingsoft.ai.hanhai.basematrix.common.core.validate.EditGroup;
import com.kingsoft.ai.hanhai.basematrix.common.log.annotation.Log;
import com.kingsoft.ai.hanhai.basematrix.common.log.enums.BusinessType;
import com.kingsoft.ai.hanhai.basematrix.common.mybatis.core.page.PageQuery;
import com.kingsoft.ai.hanhai.basematrix.common.mybatis.core.page.TableDataInfo;
import com.kingsoft.ai.hanhai.basematrix.common.web.core.BaseController;
import com.kingsoft.ai.hanhai.basematrix.system.config.UserChangeEventPublisher;
import com.kingsoft.ai.hanhai.basematrix.system.domain.bo.SysSpaceBo;
import com.kingsoft.ai.hanhai.basematrix.system.domain.vo.SysSpaceVo;
import com.kingsoft.ai.hanhai.basematrix.system.domain.vo.SysUserVo;
import com.kingsoft.ai.hanhai.basematrix.system.service.ISysSpaceService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 空间管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/space")
public class SysSpaceController extends BaseController {

    private final ISysSpaceService spaceService;

    /**
     * 获取空间信息列表
     */
    @SaCheckPermission("system:space:list")
    @GetMapping("/list")
    public TableDataInfo<SysSpaceVo> list(SysSpaceBo space, PageQuery pageQuery) {
        return spaceService.selectPageSpaceList(space, pageQuery);
    }

    /**
     * 获取空间信息详细列表
     */
    @SaCheckPermission("system:space:list")
    @GetMapping("/listDetails")
    public TableDataInfo<SysSpaceVo> listDetails(SysSpaceBo space, PageQuery pageQuery) {
        return spaceService.selectPageSpaceDetailList(space, pageQuery);
    }

    /**
     * 获取空间信息详细
     */
    @SaCheckPermission("system:space:query")
    @GetMapping("/{id}")
    public R<SysSpaceVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        SysSpaceVo space = null;
        SysSpaceBo bo = new SysSpaceBo();
        bo.setSpaceId(id);
        TableDataInfo<SysSpaceVo> spaces = spaceService.selectPageSpaceDetailList(bo, new PageQuery());
        if (!spaces.getRows().isEmpty()) {
            space = spaces.getRows().get(0);
        }
        return R.ok(space);
    }

    /**
     * 新增空间
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:add")
    @Log(title = "空间管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysSpaceBo space) throws ServiceException {
        if (!spaceService.checkSpaceNameUnique(space)) {
            return R.fail("创建空间'" + space.getSpaceName() + "'失败，空间名称已存在");
        }
        return toAjax(spaceService.insertSpace(space));
    }

    /**
     * 修改空间
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:edit")
    @Log(title = "空间管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysSpaceBo bo) throws ServiceException {
        return toAjax(spaceService.updateSpace(bo));
    }

    /**
     * 删除空间
     *
     * @param spaceId 空间ID
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:remove")
    @Log(title = "空间管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{spaceId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> remove(@PathVariable Long spaceId) {
        int flag = spaceService.deleteSpaceById(spaceId);
        if (flag > 0) {
            List<Long> userIds = spaceService.selectUserIdsBySpaceId(spaceId);
            log.info("空间已删除：{} 删除用户：{}", spaceId, userIds);
            UserChangeEventPublisher.publishSpaceDeleteEvent(userIds);
        }
        return toAjax(flag);
    }

    /**
     * 批量从空间移除用户
     *
     * @param bo 业务参数
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY,
        TenantConstants.SPACE_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:userremove")
    @Log(title = "空间管理", businessType = BusinessType.GRANT)
    @PutMapping("/addUser/cancelAll")
    public R<Void> deleteSpaceUserAll(@RequestBody SysSpaceBo bo) {
        // 从空间移除用户
        int result = spaceService.deleteSpaceUsers(bo.getSpaceId(), bo.getSpaceUserIds());

        // 发布空间变更事件，强制用户下线重新获取权限
        if (result > 0 && bo.getSpaceUserIds() != null && bo.getSpaceUserIds().length > 0) {
            log.info("从空间移除用户：{} ，空间：{}", bo.getSpaceUserIds(), bo.getSpaceId());
            UserChangeEventPublisher.publishSpaceChangeEvent(List.of(bo.getSpaceUserIds()));
        }

        return toAjax(result);
    }

    /**
     * 批量添加用户到空间
     *
     * @param bo 业务参数
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY,
        TenantConstants.SPACE_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:useradd")
    @Log(title = "空间管理", businessType = BusinessType.GRANT)
    @PutMapping("/addUser/selectAll")
    public R<Void> addSpaceUserAll(@RequestBody SysSpaceBo bo) {
        // 添加用户到空间
        int result = spaceService.addSpaceUsers(bo.getSpaceId(), bo.getSpaceUserIds());

        // 发布空间变更事件，强制用户下线重新获取权限
        if (result > 0 && bo.getSpaceUserIds() != null && bo.getSpaceUserIds().length > 0) {
            log.info("添加用户到空间：{} ，空间：{}", bo.getSpaceUserIds(), bo.getSpaceId());
            UserChangeEventPublisher.publishSpaceChangeEvent(List.of(bo.getSpaceUserIds()));
        }

        return toAjax(result);
    }

    /**
     * 查询空间下用户列表
     *
     * @param bo 查询条件
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY,
        TenantConstants.SPACE_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:userlist")
    @GetMapping("/listUser")
    public TableDataInfo<SysUserVo> listSpaceUsers(SysSpaceBo bo, PageQuery pageQuery) {
        return spaceService.selectSpaceUserList(bo, pageQuery);
    }

    /**
     * 查询当前租户下未分配到当前空间下用户列表
     *
     * @param spaceId 空间ID
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY,
        TenantConstants.SPACE_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:space:userlist")
    @GetMapping("/listUserNotInSpace")
    public TableDataInfo<SysUserVo> listNotInSpaceUsers(Long spaceId, String userName, PageQuery pageQuery) {
        return spaceService.selectNotInSpaceUserList(spaceId, userName, pageQuery);
    }

}
