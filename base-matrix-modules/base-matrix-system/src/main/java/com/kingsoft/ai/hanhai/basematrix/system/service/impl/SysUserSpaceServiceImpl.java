package com.kingsoft.ai.hanhai.basematrix.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kingsoft.ai.hanhai.basematrix.common.core.utils.StreamUtils;
import com.kingsoft.ai.hanhai.basematrix.system.domain.SysUserSpace;
import com.kingsoft.ai.hanhai.basematrix.system.mapper.SysUserSpaceMapper;
import com.kingsoft.ai.hanhai.basematrix.system.service.ISysUserSpaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Service
public class SysUserSpaceServiceImpl implements ISysUserSpaceService {
    private final SysUserSpaceMapper userSpaceMapper;

    @Override
    public List<Long> selectSpaceIdsByUserId(Long userId) {
        List<SysUserSpace> sysUserSpaces = userSpaceMapper.selectList(new LambdaQueryWrapper<SysUserSpace>().eq(SysUserSpace::getUserId, userId));
        if (CollUtil.isNotEmpty(sysUserSpaces)) {
            return StreamUtils.toList(sysUserSpaces, SysUserSpace::getSpaceId);
        }
        return List.of();
    }
}
