package com.kingsoft.ai.hanhai.basematrix.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.kingsoft.ai.hanhai.basematrix.system.domain.SysSpace;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 空间信息视图对象 sys_space
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysSpace.class)
public class SysSpaceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 空间ID
     */
    private Long spaceId;

    /**
     * 空间名称
     */
    private String spaceName;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 管理员名称列表
     */
    private List<SysUserVo> spaceAdmins;

    /**
     * 管理员ID列表
     */
    private List<Long> spaceAdminIds;

    /**
     * 空间下用户数量
     */
    private Long userNumbers;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
