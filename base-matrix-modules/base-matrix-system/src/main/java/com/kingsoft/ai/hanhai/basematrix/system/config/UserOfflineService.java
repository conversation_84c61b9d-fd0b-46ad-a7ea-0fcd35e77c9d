package com.kingsoft.ai.hanhai.basematrix.system.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.dto.UserOfflineNotificationDto;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.event.UserChangeEvent;
import com.kingsoft.ai.hanhai.basematrix.common.core.utils.SpringUtils;
import com.kingsoft.ai.hanhai.basematrix.common.json.utils.JsonUtils;
import com.kingsoft.ai.hanhai.basematrix.resource.api.RemoteMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户下线服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserOfflineService {
    @DubboReference
    private RemoteMessageService remoteMessageService;

    /**
     * 处理用户变更事件，强制用户下线
     *
     * @param event 用户变更事件
     */
    public void handleUserChangeEvent(UserChangeEvent event) {
        if (ObjectUtil.isNull(event) || CollUtil.isEmpty(event.getUserIds())) {
            log.info("用户变更事件为空");
            return;
        }
        log.info("处理用户变更事件: 类型={}, 用户IDs={}, 原因={}",
            event.getChangeType(), event.getUserIds(), event.getReason());

        // 创建下线通知
        UserOfflineNotificationDto notification = createNotification(event.getChangeType());

        // 强制用户下线并发送通知
        for (Long userId : event.getUserIds()) {
            forceUserOffline(userId, notification);
        }
    }

    /**
     * 强制指定用户下线
     *
     * @param userId       用户ID
     * @param notification 下线通知
     */
    public void forceUserOffline(Long userId, UserOfflineNotificationDto notification) {
        try {
            //1.获取当前在线的token
            List<String> tokenList = getUserAllTokens(userId);
            if (CollUtil.isEmpty(tokenList)) {
                log.debug("用户 {} 当前没有在线token", userId);
                return;
            }
            log.info("发送下线通知给用户:{}", userId);
            //2.发送下线通知，
            sendOfflineNotification(userId, notification);
            log.info("强制用户 {} 下线，共 {} 个token", userId, tokenList.size());
            //3.根据token强制下线
            SpringUtils.getBean(UserOfflineService.class).delayForceLogout(tokenList);

        } catch (Exception e) {
            log.error("强制用户 {} 下线失败", userId, e);
        }
    }

    /**
     * 延迟强制下线
     *
     * @param tokenList token列表
     */
    public void delayForceLogout(List<String> tokenList) {
        // 强制下线所有token
        for (String token : tokenList) {
            try {
                StpUtil.logoutByTokenValue(token);
                log.info("强制下线token: {}", token);
            } catch (NotLoginException ignored) {
                // token已经失效，忽略
                log.warn("token已经失效，忽略");
            }
        }

    }

    /**
     * 发送下线通知
     *
     * @param userId       用户ID
     * @param notification 通知内容
     */
    private void sendOfflineNotification(Long userId, UserOfflineNotificationDto notification) {
        //暂时只需要message
        String notificationJson = JsonUtils.toJsonString(notification.getMessage());
        boolean sseSuccess = false;
        // 尝试通过SSE发送通知
        try {
            remoteMessageService.publishMessage(List.of(userId), notificationJson);
            sseSuccess = true;
            log.info("通过SSE发送下线通知给用户: {}", userId);
        } catch (Exception e) {
            log.warn("SSE发送下线通知失败: {}", e.getMessage());
        }

        // 如果发送失败，记录错误日志
        if (!sseSuccess) {
            log.error("所有实时推送方式都失败，用户 {} 可能无法及时收到下线通知", userId);
        }
    }

    /**
     * 获取用户所有token
     *
     * @param userId 用户ID
     * @return token列表
     */
    private List<String> getUserAllTokens(Long userId) {
        try {
            // 构造loginId，格式与登录时保持一致
            String loginId = "sys_user" + ":" + userId;
            return StpUtil.getTokenValueListByLoginId(loginId);
        } catch (Exception e) {
            log.error("获取用户 {} 的token列表失败", userId, e);
            return List.of();
        }
    }

    /**
     * 根据变更类型创建通知
     *
     * @param changeType 变更类型
     * @return 下线通知
     */
    private UserOfflineNotificationDto createNotification(UserChangeEvent.UserChangeType changeType) {
        return switch (changeType) {
            case PASSWORD_CHANGE -> UserOfflineNotificationDto.passwordChanged();
            case USER_DISABLE -> UserOfflineNotificationDto.userDisabled();
            case USER_DELETE -> UserOfflineNotificationDto.userDeleted();
            case ROLE_CHANGE -> UserOfflineNotificationDto.roleChanged();
            case PERMISSION_CHANGE -> UserOfflineNotificationDto.permissionChanged();
            case SPACE_CHANGE -> UserOfflineNotificationDto.spaceChanged();
            case ADMIN_RESET_PASSWORD -> UserOfflineNotificationDto.adminResetPassword();
            case USER_STATUS_CHANGE -> UserOfflineNotificationDto.userDisabled();
            case USER_EDIT -> UserOfflineNotificationDto.userEdit();
            case SPACE_DELETE -> UserOfflineNotificationDto.spaceDelete();
            default -> new UserOfflineNotificationDto("系统变更", "您的账户信息已发生变更，请重新登录");
        };
    }
}
