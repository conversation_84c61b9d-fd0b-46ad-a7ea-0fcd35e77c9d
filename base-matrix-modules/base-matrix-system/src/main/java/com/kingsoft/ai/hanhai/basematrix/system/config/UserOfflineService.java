package com.kingsoft.ai.hanhai.basematrix.system.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.kingsoft.ai.hanhai.basematrix.common.core.domain.event.UserChangeEvent;
import com.kingsoft.ai.hanhai.basematrix.common.json.utils.JsonUtils;
import com.kingsoft.ai.hanhai.basematrix.resource.api.RemoteMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户下线服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserOfflineService {
    @DubboReference
    private RemoteMessageService remoteMessageService;

    /**
     * 处理用户变更事件，强制用户下线
     *
     * @param event 用户变更事件
     */
    public void handleUserChangeEvent(UserChangeEvent event) {
        if (ObjectUtil.isNull(event) || CollUtil.isEmpty(event.getUserIds())) {
            log.info("用户变更事件为空");
            return;
        }
        log.info("处理下线事件: {}", JsonUtils.toJsonString(event));
        // 强制用户下线并发送通知
        for (Long userId : event.getUserIds()) {
            forceUserOffline(userId, event.getReason());
        }
    }

    /**
     * 强制指定用户下线
     *
     * @param userId       用户ID
     * @param reason 下线通知
     */
    public void forceUserOffline(Long userId, String reason) {
        try {
            //1.获取当前在线的token
            List<String> tokenList = getUserAllTokens(userId);
            if (CollUtil.isEmpty(tokenList)) {
                log.debug("用户 {} 当前没有在线token", userId);
                return;
            }
            log.info("发送下线通知给用户:{}", userId);
            //2.发送下线通知，
            sendOfflineNotification(userId, reason);
            log.info("强制用户 {} 下线，共 {} 个token", userId, tokenList.size());
            //3.根据token强制下线
            forceLogout(tokenList);
        } catch (Exception e) {
            log.error("强制用户 {} 下线失败", userId, e);
        }
    }

    /**
     * 强制下线
     *
     * @param tokenList token列表
     */
    public void forceLogout(List<String> tokenList) {
        // 强制下线所有token
        for (String token : tokenList) {
            try {
                StpUtil.logoutByTokenValue(token);
                log.info("强制下线token: {}", token);
            } catch (NotLoginException ignored) {
                // token已经失效，忽略
                log.warn("token已经失效，忽略");
            }
        }

    }

    /**
     * 发送下线通知
     *
     * @param userId       用户ID
     * @param reason 通知内容
     */
    private void sendOfflineNotification(Long userId, String reason) {
        String notificationJson = JsonUtils.toJsonString(reason);
        // 尝试通过SSE发送通知
        try {
            remoteMessageService.publishMessage(List.of(userId), notificationJson);
            log.info("通过SSE发送下线通知给用户: {}", userId);
        } catch (Exception e) {
            log.error("SSE发送下线通知失败: {}", e.getMessage());
        }
    }

    /**
     * 获取用户所有token
     *
     * @param userId 用户ID
     * @return token列表
     */
    private List<String> getUserAllTokens(Long userId) {
        try {
            // 构造loginId，格式与登录时保持一致
            String loginId = "sys_user" + ":" + userId;
            return StpUtil.getTokenValueListByLoginId(loginId);
        } catch (Exception e) {
            log.error("获取用户 {} 的token列表失败", userId, e);
            return List.of();
        }
    }
}
