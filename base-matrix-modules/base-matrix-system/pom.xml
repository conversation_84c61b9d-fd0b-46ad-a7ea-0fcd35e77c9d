<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kingsoft</groupId>
        <artifactId>base-matrix-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>base-matrix-system</artifactId>

    <description>
        base-matrix-system系统模块
    </description>

    <dependencies>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-sentinel</artifactId>
        </dependency>

        <!-- base-matrix Common Log -->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-service-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-encrypt</artifactId>
        </dependency>

        <!-- base-matrix Api System -->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-api-resource</artifactId>
        </dependency>

        <!-- base-matrix Api System -->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-api-workflow</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
