package com.kingsoft.ai.hanhai.basematrix.common.translation.core.impl;

import com.kingsoft.ai.hanhai.basematrix.common.translation.annotation.TranslationType;
import com.kingsoft.ai.hanhai.basematrix.common.translation.constant.TransConstant;
import com.kingsoft.ai.hanhai.basematrix.common.translation.core.TranslationInterface;
import com.kingsoft.ai.hanhai.basematrix.system.api.RemoteUserService;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * 用户名翻译实现
 *
 * <AUTHOR> Li
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_NAME)
public class UserNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public String translation(Object key, String other) {
        return remoteUserService.selectUserNameById((Long) key);
    }
}
