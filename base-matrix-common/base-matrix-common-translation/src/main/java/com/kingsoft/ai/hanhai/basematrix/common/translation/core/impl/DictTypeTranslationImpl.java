package com.kingsoft.ai.hanhai.basematrix.common.translation.core.impl;

import com.kingsoft.ai.hanhai.basematrix.common.core.service.DictService;
import com.kingsoft.ai.hanhai.basematrix.common.core.utils.StringUtils;
import com.kingsoft.ai.hanhai.basematrix.common.translation.annotation.TranslationType;
import com.kingsoft.ai.hanhai.basematrix.common.translation.constant.TransConstant;
import com.kingsoft.ai.hanhai.basematrix.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 字典翻译实现
 *
 * <AUTHOR> Li
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.DICT_TYPE_TO_LABEL)
public class DictTypeTranslationImpl implements TranslationInterface<String> {

    private final DictService dictService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String && StringUtils.isNotBlank(other)) {
            return dictService.getDictLabel(other, key.toString());
        }
        return null;
    }
}
