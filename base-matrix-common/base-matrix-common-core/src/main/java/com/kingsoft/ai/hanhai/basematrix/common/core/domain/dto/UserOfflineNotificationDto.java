package com.kingsoft.ai.hanhai.basematrix.common.core.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户下线通知DTO
 *
 * <AUTHOR>
 */
@Data
public class UserOfflineNotificationDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通知类型
     */
    private String type = "USER_OFFLINE";

    /**
     * 下线原因
     */
    private String reason;

    /**
     * 下线原因代码
     */
    private String reasonCode;

    /**
     * 提示消息
     */
    private String message;

    /**
     * 是否强制下线
     */
    private Boolean forceLogout = true;

    /**
     * 延迟时间（秒），给用户保存数据的时间
     */
    private Integer delaySeconds = 5;

    /**
     * 时间戳
     */
    private Long timestamp;

    public UserOfflineNotificationDto() {
        this.timestamp = System.currentTimeMillis();
    }

    public UserOfflineNotificationDto(String reason, String message) {
        this();
        this.reason = reason;
        this.message = message;
    }

    public  UserOfflineNotificationDto(String reason, String reasonCode, String message) {
        this(reason, message);
        this.reasonCode = reasonCode;
    }

    /**
     * 创建密码修改通知
     */
    public static UserOfflineNotificationDto passwordChanged() {
        return new UserOfflineNotificationDto(
            "密码已修改",
            "PASSWORD_CHANGED",
            "您的密码已被修改，为了账户安全，请重新登录"
        );
    }

    /**
     * 创建用户禁用通知
     */
    public static UserOfflineNotificationDto userDisabled() {
        return new UserOfflineNotificationDto(
            "账户已被禁用",
            "USER_DISABLED",
            "您的账户已被管理员禁用，请联系管理员"
        );
    }

    /**
     * 创建用户修改通知
     */
    public static UserOfflineNotificationDto userEdit() {
        return new UserOfflineNotificationDto(
            "账户状态/角色/空间已被修改",
            "USER_EDIT",
            "您的账户已被修改账号状态/角色/空间，请重新登录"
        );
    }

    /**
     * 创建用户删除通知
     */
    public static UserOfflineNotificationDto userDeleted() {
        return new UserOfflineNotificationDto(
            "账户已被删除",
            "USER_DELETED",
            "您的账户已被删除，请联系管理员"
        );
    }

    /**
     * 创建角色变更通知
     */
    public static UserOfflineNotificationDto roleChanged() {
        return new UserOfflineNotificationDto(
            "角色权限已变更",
            "ROLE_CHANGED",
            "您的角色权限已发生变更，请重新登录以获取最新权限"
        );
    }

    /**
     * 创建权限变更通知
     */
    public static UserOfflineNotificationDto permissionChanged() {
        return new UserOfflineNotificationDto(
            "权限已变更",
            "PERMISSION_CHANGED",
            "您的权限已发生变更，请重新登录以获取最新权限"
        );
    }

    /**
     * 创建空间调整通知
     */
    public static UserOfflineNotificationDto spaceChanged() {
        return new UserOfflineNotificationDto(
            "空间权限已调整",
            "SPACE_CHANGED",
            "您的空间权限已被调整，请重新登录以获取最新权限"
        );
    }

    /**
     * 创建空间删除通知
     */
    public static UserOfflineNotificationDto spaceDelete() {
        return new UserOfflineNotificationDto(
            "空间已删除",
            "SPACE_DELETE",
            "您的拥有的空间权限已被调整，请重新登录以获取最新权限"
        );
    }

    /**
     * 创建管理员重置密码通知
     */
    public static UserOfflineNotificationDto adminResetPassword() {
        return new UserOfflineNotificationDto(
            "密码已被重置",
            "ADMIN_RESET_PASSWORD",
            "您的密码已被管理员重置，请重新登录"
        );
    }
}
