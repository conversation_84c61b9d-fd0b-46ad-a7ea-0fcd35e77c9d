package com.kingsoft.ai.hanhai.basematrix.common.core.constant;

/**
 * 租户常量信息
 *
 * <AUTHOR> Li
 */
public interface TenantConstants {

    /**
     * 超级管理员ID
     */
    Long SUPER_ADMIN_ID = 1L;

    /**
     * 超级管理员角色 roleKey
     */
    String SUPER_ADMIN_ROLE_KEY = "superadmin";

    /**
     * 租户管理员角色 roleKey
     */
    String TENANT_ADMIN_ROLE_KEY = "admin";

    /**
     * 租户管理员角色名称
     */
    String TENANT_ADMIN_ROLE_NAME = "管理员";

    /**
     * 空间管理员角色 roleKey
     */
    String SPACE_ADMIN_ROLE_KEY = "spaceadmin";

    /**
     * 空间管理员角色名称
     */
    String SPACE_ADMIN_ROLE_NAME = "空间管理员";

    /**
     * 默认租户ID
     */
    String DEFAULT_TENANT_ID = "000000";

    /**
     * 租户状态正常
     */
    String TENANT_STATUS_NORMAL = "0";
    /**
     * 租户状态停用
     */
    String TENANT_STATUS_DISABLE = "1";


}
