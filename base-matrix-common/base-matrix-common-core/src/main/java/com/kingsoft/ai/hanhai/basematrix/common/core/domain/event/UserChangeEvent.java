package com.kingsoft.ai.hanhai.basematrix.common.core.domain.event;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户变更事件
 *
 * <AUTHOR>
 */
@Data
public class UserChangeEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 事件类型
     */
    private UserChangeType changeType;

    /**
     * 受影响的用户ID列表
     */
    private List<Long> userIds;

    /**
     * 操作者ID
     */
    private Long operatorId;

    /**
     * 操作者名称
     */
    private String operatorName;

    /**
     * 变更原因/描述
     */
    private String reason;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 扩展数据
     */
    private Object extData;

    /**
     * 事件时间戳
     */
    private Long timestamp;

    public UserChangeEvent() {
        this.timestamp = System.currentTimeMillis();
    }

    public UserChangeEvent(UserChangeType changeType, List<Long> userIds, String reason) {
        this();
        this.changeType = changeType;
        this.userIds = userIds;
        this.reason = reason;
    }

    public UserChangeEvent(UserChangeType changeType, Long userId, String reason) {
        this(changeType, List.of(userId), reason);
    }

    /**
     * 用户变更类型枚举
     */
    public enum UserChangeType {
        /**
         * 租户删除
         */
        TENANT_DELETE("租户删除"),
        /**
         * 租户禁用
         */
        TENANT_DISABLE("租户禁用"),
        /**
         * 密码修改
         */
        PASSWORD_CHANGE("密码修改"),

        /**
         * 用户禁用
         */
        USER_DISABLE("用户禁用"),

        /**
         * 用户删除
         */
        USER_DELETE("用户删除"),
        /**
         * 用户修改
         */
        USER_EDIT("用户修改"),


        /**
         * 角色变更
         */
        ROLE_CHANGE("角色变更"),
        /**
         * 角色变更
         */
        ROLE_STATUS_CHANGE("角色状态变更"),
        /**
         * 角色删除
         */
        ROLE_DELETE("角色删除"),
        /**
         * 权限变更
         */
        PERMISSION_CHANGE("权限变更"),

        /**
         * 空间调整
         */
        SPACE_CHANGE("空间调整"),
        /**
         * 空间删除
         */
        SPACE_DELETE("空间删除"),
        /**
         * 管理员重置密码
         */
        ADMIN_RESET_PASSWORD("管理员重置密码"),

        /**
         * 用户状态变更
         */
        USER_STATUS_CHANGE("用户状态变更");

        private final String description;

        UserChangeType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
