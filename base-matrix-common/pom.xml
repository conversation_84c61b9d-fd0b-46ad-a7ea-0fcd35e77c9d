<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kingsoft</groupId>
        <artifactId>Base-Matrix-backend</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>base-matrix-common-bom</module>
        <module>base-matrix-common-alibaba-bom</module>
        <module>base-matrix-common-log</module>
        <module>base-matrix-common-service-impl</module>
        <module>base-matrix-common-excel</module>
        <module>base-matrix-common-core</module>
        <module>base-matrix-common-redis</module>
        <module>base-matrix-common-doc</module>
        <module>base-matrix-common-security</module>
        <module>base-matrix-common-satoken</module>
        <module>base-matrix-common-web</module>
        <module>base-matrix-common-mybatis</module>
        <module>base-matrix-common-job</module>
        <module>base-matrix-common-dubbo</module>
        <module>base-matrix-common-seata</module>
        <module>base-matrix-common-loadbalancer</module>
        <module>base-matrix-common-oss</module>
        <module>base-matrix-common-ratelimiter</module>
        <module>base-matrix-common-idempotent</module>
        <module>base-matrix-common-mail</module>
        <module>base-matrix-common-sms</module>
        <module>base-matrix-common-logstash</module>
        <module>base-matrix-common-elasticsearch</module>
        <module>base-matrix-common-sentinel</module>
        <module>base-matrix-common-skylog</module>
        <module>base-matrix-common-prometheus</module>
        <module>base-matrix-common-translation</module>
        <module>base-matrix-common-sensitive</module>
        <module>base-matrix-common-json</module>
        <module>base-matrix-common-encrypt</module>
        <module>base-matrix-common-tenant</module>
        <module>base-matrix-common-websocket</module>
        <module>base-matrix-common-social</module>
        <module>base-matrix-common-nacos</module>
        <module>base-matrix-common-bus</module>
        <module>base-matrix-common-sse</module>
    </modules>

    <artifactId>base-matrix-common</artifactId>
    <packaging>pom</packaging>

    <description>
        base-matrix-common通用模块
    </description>

</project>
