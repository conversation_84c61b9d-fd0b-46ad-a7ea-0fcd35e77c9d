<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kingsoft</groupId>
        <artifactId>base-matrix-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>base-matrix-common-satoken</artifactId>

    <description>
        base-matrix-common-satoken 权限认证服务
    </description>

    <dependencies>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
        </dependency>

        <!-- base-matrix Api System -->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-api-system</artifactId>
        </dependency>

        <!-- base-matrix Common Redis-->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

    </dependencies>

</project>
