<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kingsoft</groupId>
        <artifactId>base-matrix-example</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>base-matrix-test-mq</artifactId>

    <description>
        base-matrix-test-mq 案例项目
    </description>

    <dependencies>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-tenant</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.kingsoft</groupId>
                    <artifactId>base-matrix-common-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
