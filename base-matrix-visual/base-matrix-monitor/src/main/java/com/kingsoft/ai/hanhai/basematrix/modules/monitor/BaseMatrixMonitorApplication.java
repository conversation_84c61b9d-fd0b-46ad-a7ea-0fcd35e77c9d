package com.kingsoft.ai.hanhai.basematrix.modules.monitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 监控中心
 *
 * <AUTHOR>
 */
@SpringBootApplication
public class BaseMatrixMonitorApplication {
    public static void main(String[] args) {
        SpringApplication.run(BaseMatrixMonitorApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  监控中心启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
