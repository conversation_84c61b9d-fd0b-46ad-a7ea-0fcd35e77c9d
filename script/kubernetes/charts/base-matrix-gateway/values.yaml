profile: dev
nacos:
  server: nacos-cs.base-matrix.svc.cluster.local:8848
  username: nacos
  password: nacos
  discovery:
    group: DEFAULT_GROUP

# 副本数
replicaCount: 1

# 镜像配置
image:
  repository: harbor.inner.ai.kingsoft.com:11180/base-matrix/base-matrix-gateway
  tag: "v3.1.0"
  pullPolicy: IfNotPresent

# 镜像拉取密钥
imagePullSecrets: []
# - name: regcred

# 容器端口
containerPorts:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP

# 环境变量
env: {}
# KEY: value

# 从Secret获取的环境变量
envFromSecret: {}
  # KEY:
  #   secretName: my-secret
#   secretKey: my-key

# 资源限制
resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

# 健康检查
#livenessProbe:
#  httpGet:
#    path: /
#    port: http
#  initialDelaySeconds: 30
#  periodSeconds: 10
#  timeoutSeconds: 5
#  failureThreshold: 3
#
#readinessProbe:
#  httpGet:
#    path: /
#    port: http
#  initialDelaySeconds: 5
#  periodSeconds: 10
#  timeoutSeconds: 5
#  failureThreshold: 3

startupProbe: {}
  # httpGet:
  #   path: /
  #   port: http
  # failureThreshold: 30
# periodSeconds: 10

# 卷挂载
volumeMounts: []
  # - name: config
#   mountPath: /etc/config

volumes: []
  # - name: config
  #   configMap:
#     name: my-config

# 节点选择
nodeSelector: {}

# 亲和性配置
affinity: {}

# 容忍配置
tolerations: []

# Pod安全上下文
securityContext: {}
# fsGroup: 2000

# Pod注解
podAnnotations: {}

# 更新策略
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

# 保留的历史版本数
revisionHistoryLimit: 3

ingress:
  className: "nginx"
  host: dev.inner.ai.kingsoft.com
