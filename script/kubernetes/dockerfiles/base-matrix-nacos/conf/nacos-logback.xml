<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 1999-2018 Alibaba Group Holding Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<configuration debug="false" scan="false" packagingData="true">

    <!-- 控制台输出（推荐K8s环境使用） -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %p [%-5t:%c{2}] %m%n</pattern>
        </encoder>
    </appender>

    <!-- 日志级别配置 -->
    <logger name="com.alibaba.nacos.client" level="${com.alibaba.nacos.config.log.level:-info}" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.alibaba.nacos.common.remote.client" level="${com.alibaba.nacos.log.level:-info}" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.alibaba.nacos.shaded.io.grpc" level="${com.alibaba.nacos.log.level:-info}" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.alibaba.nacos.client.config" level="${com.alibaba.nacos.config.log.level:-info}" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.alibaba.nacos.client.naming" level="${com.alibaba.nacos.naming.log.level:-info}" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- 根日志 -->
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
