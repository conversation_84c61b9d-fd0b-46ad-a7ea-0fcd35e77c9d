FROM bellsoft/liberica-openjdk-debian:17.0.11-cds
LABEL maintainer="pader <<EMAIL>>"

# 设置环境变量
ENV MODE="cluster" \
    PREFER_HOST_MODE="ip"\
    BASE_DIR="/home/<USER>" \
    CLASSPATH=".:/home/<USER>/conf:$CLASSPATH" \
    CLUSTER_CONF="/home/<USER>/conf/cluster.conf" \
    FUNCTION_MODE="all" \
    JAVA_HOME="/usr/lib/jvm/jdk-17.0.11-bellsoft-x86_64/" \
    NACOS_USER="nacos" \
    JAVA="/usr/lib/jvm/jdk-17.0.11-bellsoft-x86_64/bin/java" \
    JVM_XMS="1g" \
    JVM_XMX="1g" \
    JVM_XMN="512m" \
    JVM_MS="128m" \
    JVM_MMS="320m" \
    NACOS_DEBUG="n" \
    TOMCAT_ACCESSLOG_ENABLED="false" \
    TIME_ZONE="Asia/Shanghai"

ARG NACOS_VERSION=2.5.1
ARG HOT_FIX_FLAG=""

WORKDIR $BASE_DIR

RUN set -x \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
        tzdata \
        procps \
        net-tools \
        iputils-ping \
        curl \
        wget \
        vim-tiny \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装 Nacos
RUN set -x \
    && mkdir -p /home/<USER>
    && ln -snf /usr/share/zoneinfo/$TIME_ZONE /etc/localtime && echo $TIME_ZONE > /etc/timezone

COPY base-matrix-visual/base-matrix-nacos/target/base-matrix-nacos.jar /home/<USER>/target/nacos-server.jar
COPY script/kubernetes/dockerfiles/base-matrix-nacos/bin/docker-startup.sh bin/docker-startup.sh
COPY script/kubernetes/dockerfiles/base-matrix-nacos/conf/application.properties conf/application.properties
#COPY script/kubernetes/dockerfiles/base-matrix-nacos/conf/nacos-logback.xml conf/nacos-logback.xml

# 设置启动日志目录
RUN mkdir -p logs \
	&& touch logs/start.out \
	&& ln -sf /dev/stdout logs/start.out \
	&& ln -sf /dev/stderr logs/start.out \
    && chmod +x bin/docker-startup.sh

EXPOSE 8848
ENTRYPOINT ["bash","bin/docker-startup.sh"]
