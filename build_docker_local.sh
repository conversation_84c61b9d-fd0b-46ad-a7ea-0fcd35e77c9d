#!/bin/bash
set -euo pipefail

# 默认配置
SKIP_BUILD=true
SKIP_IMAGE=false
SKIP_PUSH=false
BASE_PATH=.
DEFAULT_VERSION=v3.1.2-yzh
VERSION=$DEFAULT_VERSION
DEFAULT_PLATFORM="linux/amd64"
PLATFORM=$DEFAULT_PLATFORM
MULTI_ARCH=false

# 融合devharbor地址
DEVHARBOR_REGISTRY="harbor.inner.ai.kingsoft.com:11180/base-matrix"

export JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/graalvm-ce-17.0.9/Contents/Home"

# 服务分类定义
INFRA_SERVICES="base-matrix-monitor base-matrix-nacos base-matrix-seata-server base-matrix-sentinel-dashboard base-matrix-snailjob-server"
BUSINESS_SERVICES="base-matrix-auth base-matrix-gateway base-matrix-gen base-matrix-job base-matrix-resource base-matrix-system base-matrix-workflow"
ALL_SERVICES="$INFRA_SERVICES $BUSINESS_SERVICES"

# 显示帮助信息
show_help() {
    echo "Usage: $0 [options] [services...]"
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --version VER       Set image version (default: $DEFAULT_VERSION)"
    echo "  -p, --platform PLATFORM Set target platform (default: $DEFAULT_PLATFORM)"
    echo "  --multi-arch            Build multi-architecture images (linux/amd64,linux/arm64)"
    echo "  --skip-build            Skip Maven build step"
    echo "  --skip-image            Skip Docker image build step"
    echo "  --skip-push             Skip Docker image push step"
    echo "  --infra                 Build all infrastructure services"
    echo "  --business              Build all business services"
    echo "  --all                   Build all services (default)"
    echo ""
    echo "Platform Options:"
    echo "  linux/amd64             Intel/AMD 64-bit (default)"
    echo "  linux/arm64             ARM 64-bit (Apple Silicon, ARM servers)"
    echo "  linux/arm/v7            ARM 32-bit"
    echo "  --multi-arch            Build for both linux/amd64 and linux/arm64"
    echo ""
    echo "Service Groups:"
    echo "  Infrastructure services: $INFRA_SERVICES"
    echo ""
    echo "  Business services: $BUSINESS_SERVICES"
    echo ""
    echo "Examples:"
    echo "  $0 -v v3.2.0                              # Build all services with custom version"
    echo "  $0 --platform linux/arm64 --infra         # Build infra services for ARM64"
    echo "  $0 --multi-arch --business                 # Build business services for multiple architectures"
    echo "  $0 -p linux/amd64 -v test base-matrix-system  # Build specific service for AMD64"
    echo "  $0 --skip-build --platform linux/arm64    # Skip build and create ARM64 images"
    echo "  $0 --skip-push --multi-arch                # Build multi-arch but don't push"
    exit 0
}

# 获取服务模块路径
get_module_path() {
    case "$1" in
        base-matrix-monitor) echo "base-matrix-visual/base-matrix-monitor" ;;
        base-matrix-nacos) echo "base-matrix-visual/base-matrix-nacos" ;;
        base-matrix-seata-server) echo "base-matrix-visual/base-matrix-seata-server" ;;
        base-matrix-sentinel-dashboard) echo "base-matrix-visual/base-matrix-sentinel-dashboard" ;;
        base-matrix-snailjob-server) echo "base-matrix-visual/base-matrix-snailjob-server" ;;
        base-matrix-auth) echo "base-matrix-auth" ;;
        base-matrix-gateway) echo "base-matrix-gateway" ;;
        base-matrix-gen) echo "base-matrix-modules/base-matrix-gen" ;;
        base-matrix-job) echo "base-matrix-modules/base-matrix-job" ;;
        base-matrix-resource) echo "base-matrix-modules/base-matrix-resource" ;;
        base-matrix-system) echo "base-matrix-modules/base-matrix-system" ;;
        base-matrix-workflow) echo "base-matrix-modules/base-matrix-workflow" ;;
        *) echo "" ;;
    esac
}

# 获取 Docker Compose 服务名
get_service_name() {
    case "$1" in
        base-matrix-monitor) echo "base-matrix-monitor" ;;
        base-matrix-nacos) echo "nacos" ;;
        base-matrix-seata-server) echo "seata-server" ;;
        base-matrix-sentinel-dashboard) echo "sentinel" ;;
        base-matrix-snailjob-server) echo "base-matrix-snailjob-server" ;;
        base-matrix-auth) echo "base-matrix-auth" ;;
        base-matrix-gateway) echo "base-matrix-gateway" ;;
        base-matrix-gen) echo "base-matrix-gen" ;;
        base-matrix-job) echo "base-matrix-job" ;;
        base-matrix-resource) echo "base-matrix-resource" ;;
        base-matrix-system) echo "base-matrix-system" ;;
        base-matrix-workflow) echo "base-matrix-workflow" ;;
        *) echo "" ;;
    esac
}


# 检查服务是否有效
is_valid_service() {
    for s in $ALL_SERVICES; do
        if [ "$s" = "$1" ]; then
            return 0
        fi
    done
    return 1
}

# 解析命令行参数
parse_args() {
    local services=""
    local has_category=0

    while [ $# -gt 0 ]; do
        case "$1" in
            -h|--help)
                show_help >&2
                exit 0 ;;

            -v|--version)
                if [ -z "$2" ]; then
                    echo "Error: Version argument is missing" >&2
                    show_help
                    exit 1
                fi
                VERSION="$2"
                shift 2 ;;

            -p|--platform)
                if [ -z "$2" ]; then
                    echo "Error: Platform argument is missing" >&2
                    show_help
                    exit 1
                fi
                PLATFORM="$2"
                MULTI_ARCH=false
                shift 2 ;;

            --multi-arch)
                MULTI_ARCH=true
                PLATFORM="linux/amd64,linux/arm64"
                shift ;;

            --skip-build)
                SKIP_BUILD=true
                shift ;;

            --skip-image)
                SKIP_IMAGE=true
                shift ;;

            --skip-push)
                SKIP_PUSH=true
                shift ;;

            --infra)
                services="$services $INFRA_SERVICES"
                has_category=1
                shift ;;

            --business)
                services="$services $BUSINESS_SERVICES"
                has_category=1
                shift ;;

            --all)
                services="$services $ALL_SERVICES"
                has_category=1
                shift ;;

            *)
                if is_valid_service "$1"; then
                    services="$services $1"
                else
                    echo "Error: Invalid service '$1'" >&2
                    show_help
                    exit 1
                fi
                shift ;;
        esac
    done

    # 如果没有指定任何服务或类别，则使用所有服务
    if [ -z "$services" ] && [ $has_category -eq 0 ]; then
        services="$ALL_SERVICES"
    fi

    # 去重
    local unique_services=""
    for service in $services; do
        if ! echo "$unique_services" | grep -q -w "$service"; then
            unique_services="$unique_services $service"
        fi
    done

    # 移除前导空格
    echo "${unique_services# }"
}

# 检查 Docker Buildx 是否可用
check_buildx() {
    if ! docker buildx version >/dev/null 2>&1; then
        echo "Error: Docker Buildx is not available. Please install Docker Buildx for multi-platform builds." >&2
        exit 1
    fi
}

# 构建单架构镜像
build_single_arch_image() {
    local service="$1"
    local image_name="base-matrix/${service}:${VERSION}"
    local dockerfile="$2"

    echo "Building $service image for $PLATFORM: $image_name"
    docker build --platform "$PLATFORM" -t "$image_name" -f "$dockerfile" "$BASE_PATH"
}

# 构建多架构镜像
build_multi_arch_image() {
    local service="$1"
    local image_name="base-matrix/${service}:${VERSION}"
    local remote_image="${DEVHARBOR_REGISTRY}/${service}:${VERSION}"
    local dockerfile="$2"

    echo "Building multi-architecture $service image for $PLATFORM"

    if [ "${SKIP_PUSH}" = false ]; then
        # 构建并直接推送多架构镜像
        docker buildx build --platform "$PLATFORM" \
            -t "$remote_image" \
            -f "$dockerfile" \
            --push \
            "$BASE_PATH"
    else
        # 只构建到本地（注意：多架构镜像无法直接加载到本地 Docker）
        echo "Warning: Multi-arch images cannot be loaded to local Docker when --skip-push is used"
        echo "Building multi-arch image to registry cache only..."
        docker buildx build --platform "$PLATFORM" \
            -t "$remote_image" \
            -f "$dockerfile" \
            "$BASE_PATH"
    fi
}

# 推送镜像到阿里云仓库
push_image_to_aliyun() {
    local service="$1"
    local local_image="base-matrix/${service}:${VERSION}"
    local remote_image="${DEVHARBOR_REGISTRY}/${service}:${VERSION}"

    echo "Tagging image: $local_image -> $remote_image"
    docker tag "$local_image" "$remote_image"

    echo "Pushing image to Aliyun registry: $remote_image"
    docker push "$remote_image"

    echo "✅ Successfully pushed $service to Aliyun registry"
}

# 主函数
main() {
    # 1. 解析命令行参数
    local services=$(parse_args "$@")

    # 2. 显示构建信息
    echo "============================================================"
    echo "Starting build and push for services (version: $VERSION):"
    for service in $services; do
        echo " - $service"
    done
    echo "Registry: $DEVHARBOR_REGISTRY"
    echo "Platform: $PLATFORM"
    echo "Multi-arch: $MULTI_ARCH"
    echo "Skip build: $SKIP_BUILD"
    echo "Skip image: $SKIP_IMAGE"
    echo "Skip push: $SKIP_PUSH"
    echo "============================================================"

    # 3. 构建项目（可选跳过）
    if [ "${SKIP_BUILD}" = false ]; then
        echo ""
        echo "🔨 Building project with Java: ${JAVA_HOME}..."
        export JAVA_HOME
        mvn -U -P dev -DskipTests=true clean install
        echo "✅ Maven build completed successfully"
    else
        echo "⏭️  Skipping Maven build as requested"
    fi

    # 4. 检查多架构构建需求
    if [ "${SKIP_IMAGE}" = false ] && [ "${MULTI_ARCH}" = true ]; then
        check_buildx
    fi

    # 5. 构建服务镜像（可选跳过）
    if [ "${SKIP_IMAGE}" = false ]; then
        echo ""
        if [ "${MULTI_ARCH}" = true ]; then
            echo "🐳 Building multi-architecture Docker images for: $PLATFORM"
        else
            echo "🐳 Building Docker images for platform: $PLATFORM"
        fi

        for service in $services; do
            # 获取模块路径
            local module_path=$(get_module_path "$service")

            if [ -z "$module_path" ]; then
                echo "Error: Module path not found for service $service" >&2
                exit 1
            fi

            # 设置镜像名称和 Dockerfile 路径
            local dockerfile="${module_path}/Dockerfile"

            if [ ! -f "$dockerfile" ]; then
                echo "Error: Dockerfile not found at $dockerfile" >&2
                exit 1
            fi

            # 根据是否多架构选择构建方式
            if [ "${MULTI_ARCH}" = true ]; then
                build_multi_arch_image "$service" "$dockerfile"
            else
                build_single_arch_image "$service" "$dockerfile"
            fi
            echo "✅ Successfully built $service image"
        done
    else
        echo "⏭️  Skipping Docker image build as requested"
    fi

    # 6. 推送镜像到阿里云仓库（可选跳过）
    if [ "${SKIP_PUSH}" = false ] && [ "${SKIP_IMAGE}" = false ] && [ "${MULTI_ARCH}" = false ]; then
        echo ""
        echo "📤 Pushing images to Aliyun registry..."
        for service in $services; do
            push_image_to_aliyun "$service"
        done
        echo "✅ All images pushed successfully"
    else
        if [ "${SKIP_PUSH}" = true ]; then
            echo "⏭️  Skipping Docker image push as requested"
        elif [ "${SKIP_IMAGE}" = true ]; then
            echo "⏭️  Skipping Docker image push (no images built)"
        elif [ "${MULTI_ARCH}" = true ]; then
            echo "ℹ️  Multi-arch images were pushed during build process"
        fi
    fi

    # 7. 显示完成摘要
    echo ""
    echo "🎉 All tasks completed successfully!"
    echo "============================================================"
    echo "Build and Push Summary:"
    echo "  Version: $VERSION"
    echo "  Registry: $DEVHARBOR_REGISTRY"
    echo "  Platform: $PLATFORM"
    echo "  Multi-arch: $MULTI_ARCH"
    echo "  Services processed:"
    for service in $services; do
        echo "    - $service"
    done
    echo "  Skip build: $SKIP_BUILD"
    echo "  Skip image: $SKIP_IMAGE"
    echo "  Skip push: $SKIP_PUSH"
    echo "============================================================"
}

# 运行主函数
main "$@"
