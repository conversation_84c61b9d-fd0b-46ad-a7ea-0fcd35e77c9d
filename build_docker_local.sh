#!/bin/bash
set -euo pipefail

# 默认配置
SKIP_BUILD=false
SKIP_IMAGE=false
BASE_PATH=.
DEFAULT_VERSION=v3.1.2-yzh
VERSION=$DEFAULT_VERSION

export JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64"

# 服务分类定义
INFRA_SERVICES="base-matrix-monitor base-matrix-nacos base-matrix-seata-server base-matrix-sentinel-dashboard base-matrix-snailjob-server"
BUSINESS_SERVICES="base-matrix-auth base-matrix-gateway base-matrix-gen base-matrix-job base-matrix-resource base-matrix-system base-matrix-workflow"
ALL_SERVICES="$INFRA_SERVICES $BUSINESS_SERVICES"

# 显示帮助信息
show_help() {
    echo "Usage: $0 [options] [services...]"
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -v, --version VER   Set image version (default: $DEFAULT_VERSION)"
    echo "  --infra             Deploy all infrastructure services"
    echo "  --business          Deploy all business services"
    echo "  --all               Deploy all services (default)"
    echo ""
    echo "Service Groups:"
    echo "  Infrastructure services: $INFRA_SERVICES"
    echo ""
    echo "  Business services: $BUSINESS_SERVICES"
    echo ""
    echo "Examples:"
    echo "  $0 -v v3.2.0                          # Deploy all services with custom version"
    echo "  $0 --version v4.0.0-rc1 --infra        # Deploy infra services with custom version"
    echo "  $0 --business -v latest                # Deploy business services with 'latest' tag"
    echo "  $0 -v test-version base-matrix-system  # Deploy specific service with custom version"
    echo "  $0 --skip-build base-matrix-gateway    # Skip build and deploy single service"
    exit 0
}

# 获取服务模块路径
get_module_path() {
    case "$1" in
        base-matrix-monitor) echo "base-matrix-visual/base-matrix-monitor" ;;
        base-matrix-nacos) echo "base-matrix-visual/base-matrix-nacos" ;;
        base-matrix-seata-server) echo "base-matrix-visual/base-matrix-seata-server" ;;
        base-matrix-sentinel-dashboard) echo "base-matrix-visual/base-matrix-sentinel-dashboard" ;;
        base-matrix-snailjob-server) echo "base-matrix-visual/base-matrix-snailjob-server" ;;
        base-matrix-auth) echo "base-matrix-auth" ;;
        base-matrix-gateway) echo "base-matrix-gateway" ;;
        base-matrix-gen) echo "base-matrix-modules/base-matrix-gen" ;;
        base-matrix-job) echo "base-matrix-modules/base-matrix-job" ;;
        base-matrix-resource) echo "base-matrix-modules/base-matrix-resource" ;;
        base-matrix-system) echo "base-matrix-modules/base-matrix-system" ;;
        base-matrix-workflow) echo "base-matrix-modules/base-matrix-workflow" ;;
        *) echo "" ;;
    esac
}

# 获取 Docker Compose 服务名
get_service_name() {
    case "$1" in
        base-matrix-monitor) echo "base-matrix-monitor" ;;
        base-matrix-nacos) echo "nacos" ;;
        base-matrix-seata-server) echo "seata-server" ;;
        base-matrix-sentinel-dashboard) echo "sentinel" ;;
        base-matrix-snailjob-server) echo "base-matrix-snailjob-server" ;;
        base-matrix-auth) echo "base-matrix-auth" ;;
        base-matrix-gateway) echo "base-matrix-gateway" ;;
        base-matrix-gen) echo "base-matrix-gen" ;;
        base-matrix-job) echo "base-matrix-job" ;;
        base-matrix-resource) echo "base-matrix-resource" ;;
        base-matrix-system) echo "base-matrix-system" ;;
        base-matrix-workflow) echo "base-matrix-workflow" ;;
        *) echo "" ;;
    esac
}


# 检查服务是否有效
is_valid_service() {
    for s in $ALL_SERVICES; do
        if [ "$s" = "$1" ]; then
            return 0
        fi
    done
    return 1
}

# 解析命令行参数
parse_args() {
    local services=""
    local has_category=0

    while [ $# -gt 0 ]; do
        case "$1" in
            -h|--help)
                show_help ;;

            -v|--version)
                if [ -z "$2" ]; then
                    echo "Error: Version argument is missing" >&2
                    show_help
                    exit 1
                fi
                VERSION="$2"
                shift 2 ;;

            --infra)
                services="$services $INFRA_SERVICES"
                has_category=1
                shift ;;

            --business)
                services="$services $BUSINESS_SERVICES"
                has_category=1
                shift ;;

            --all)
                services="$services $ALL_SERVICES"
                has_category=1
                shift ;;

            *)
                if is_valid_service "$1"; then
                    services="$services $1"
                else
                    echo "Error: Invalid service '$1'" >&2
                    show_help
                    exit 1
                fi
                shift ;;
        esac
    done

    # 如果没有指定任何服务或类别，则使用所有服务
    if [ -z "$services" ] && [ $has_category -eq 0 ]; then
        services="$ALL_SERVICES"
    fi

    # 去重
    local unique_services=""
    for service in $services; do
        if ! echo "$unique_services" | grep -q -w "$service"; then
            unique_services="$unique_services $service"
        fi
    done

    # 移除前导空格
    echo "${unique_services# }"
}

# 主函数
main() {
    # 1. 解析命令行参数
    local services=$(parse_args "$@")

    # 2. 显示部署信息
    echo "============================================================"
    echo "Starting deployment for services (version: $VERSION):"
    for service in $services; do
        echo " - $service"
    done
    echo "============================================================"

    # 3. 构建项目（可选跳过）
    if [ "${SKIP_BUILD}" = false ]; then
        echo "SKIP_BUILD is = ${SKIP_BUILD}"
        echo "Building project with Java: ${JAVA_HOME}..."
        export JAVA_HOME
        mvn -U -P dev -DskipTests=true clean install
    else
        echo "Skipping Maven build as requested"
    fi

    # 4. 构建服务镜像（可选跳过）
    if [ "${SKIP_IMAGE}" = false ]; then
        for service in $services; do
            # 获取模块路径
            local module_path=$(get_module_path "$service")

            if [ -z "$module_path" ]; then
                echo "Error: Module path not found for service $service" >&2
                exit 1
            fi

            # 设置镜像名称
            local image_name="base-matrix/${service}:${VERSION}"
            local dockerfile="${module_path}/Dockerfile"

            if [ ! -f "$dockerfile" ]; then
                echo "Error: Dockerfile not found at $dockerfile" >&2
                exit 1
            fi

            # 执行构建
            echo "Building $service image: $image_name ..."
            docker build -t "$image_name" -f "$dockerfile" "$BASE_PATH"
        done
    else
        echo "Skipping Docker image build as requested"
    fi

    # 5. 准备Docker Compose服务列表
    local compose_services=""
    for service in $services; do
        local service_name=$(get_service_name "$service")
        if [ -z "$service_name" ]; then
            echo "Error: Docker service name not found for $service" >&2
            exit 1
        fi
        compose_services="$compose_services $service_name"
    done

    # 6. 启动服务
    #echo "Starting services: ${compose_services# } ..."
    #export TAG=${VERSION}
    #docker-compose -f script/docker/docker-compose.yml up -d $compose_services

    # 7. 显示部署摘要
    echo ""
    echo "✅ All tasks completed successfully!"
    echo "============================================================"
    echo "Deployment summary:"
    echo "  Version: $VERSION"
    echo "  Services deployed:"
    for service in $services; do
        echo "    - $service"
    done
    echo "  Skip build: $SKIP_BUILD"
    echo "  Skip image: $SKIP_IMAGE"
    echo "============================================================"
}

# 运行主函数
main "$@"
